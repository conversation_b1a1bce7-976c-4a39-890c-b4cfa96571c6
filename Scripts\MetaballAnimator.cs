using UnityEngine;
using System.Runtime.InteropServices;
using Random = UnityEngine.Random;

public class MetaballAnimator : MonoBehaviour
{
    [Header("Shader Reference")]
    public Material metaballMaterial;


    public enum AnimationState { Random, ReactiveRandom, ToScreenSaver, ToRandom, ScreenSaver, ToReactiveRandom }
    [SerializeField] private AnimationState _currentState = AnimationState.ScreenSaver;
    public AnimationState CurrentState => _currentState;

    [Header("General Settings")]
    public float animationSpeed = 1.0f;
    [Range(0, 2)] public float timeMultiplier = 1f;
    [Range(0, 2)] public float cameraAnimationMultiplier = 1f;
    public float baseRandomAmplitude = 2.0f;

    [Header("Blob Properties")]
    public float blobRadiusMin = 0.5f;
    public float blobRadiusMax = 1.5f;
    public float blobColorShiftSpeed = 0.1f;
    [Range(0, 1)] public float blobColorMaxSaturation = 1.0f;
    [Range(0, 1)] public float blobColorMaxValue = 1.0f;

    [Header("Reactive State")]
    [Range(1, 50)] public int numberOfBlobs = 8;
    public Vector3 reactiveCenterPosition = new Vector3(0f, 4f, 0f);
    public float reactiveScale = 0.3f;
    public float reactiveGroupRadius = 0.8f;
    public float reactiveRandomMovementAmplitude = 2.0f;
    public float reactiveRandomOverallAudioScale = 1.0f;
    public float reactiveRandomAudioSensitivityMin = 0.5f;
    public float reactiveRandomAudioSensitivityMax = 1.5f;
    public float reactiveRandomMovementAudioScale = 1.0f;
    public float reactiveRandomRadiusAudioScale = 1.0f;
    public float reactiveRandomMaxAudioRadiusMultiplier = 2.0f;
    public float reactiveRandomSmoothing = 5.0f;

    [Header("Audio Reactivity")]
    public AudioSource audioSource;
    [Range(64, 8192)] public int audioSampleCount = 1024;
    public float audioSensitivity = 100f;
    [Range(0, 1)] public float audioSmoothness = 0.5f;

    [Header("Screen Saver State")]
    public float screenSaverScale = 0.4f; // Overall scale of the group
    public float screenSaverSpeed = 1.2f;
    public Vector3 screenSaverCenterPosition = new Vector3(0f, 4f, 0f); // Base center position
    public float screenSaverGroupRadius = 1.5f; // How tightly grouped the blobs are
    public int screenSaverBlobCount = 6; // Keep similar to random state
    public float screenSaverColorShiftSpeed = 0.3f;
    public float screenSaverHorizontalRange = 6f; // How far left/right the group moves
    public float screenSaverPadding = 1.5f; // Padding from screen edges
    public float screenSaverVerticalFloat = 0.5f; // Small vertical movement

    [Header("Transition Settings")]
    public float transitionSmoothness = 2f; // How smooth the transitions are
    public AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f); // Custom transition curve
    public float positionTransitionSpeed = 1.2f; // Speed multiplier for position transitions
    public float sizeTransitionSpeed = 0.8f; // Speed multiplier for size transitions
    public float colorTransitionSpeed = 1.5f; // Speed multiplier for color transitions

    [Header("Camera Controls")]
    public float cameraZOffset = 0.0f;
    [Range(0.1f, 10f)] public float cameraFOVFactor = 2.0f;

    [StructLayout(LayoutKind.Sequential)]
    public struct BlobData
    {
        public Vector3 position;
        public float radius;
        public Color color;
        public float audioSensitivityMultiplier;
    }

    // Private variables
    private BlobData[] _blobData;
    private ComputeBuffer _blobBuffer;
    private float[] _audioData;
    private float _currentAudioLevel;
    private float _transitionStartTime;
    private float _transitionDuration;
    private BlobData[] _startBlobData;
    private BlobData[] _targetBlobData;
    private Renderer _renderer;

    void Awake()
    {
        _renderer = GetComponent<Renderer>();
        InitializeBlobs();
    }

    void InitializeBlobs()
    {
        _audioData = new float[audioSampleCount];
        int initialBlobCount = (_currentState == AnimationState.ScreenSaver) ? screenSaverBlobCount : numberOfBlobs;

        _blobData = new BlobData[initialBlobCount];
        _startBlobData = new BlobData[initialBlobCount];
        _targetBlobData = new BlobData[initialBlobCount];

        if (_currentState == AnimationState.ScreenSaver)
            InitializeScreenSaverBlobData(_blobData, screenSaverBlobCount);
        else if (_currentState == AnimationState.ReactiveRandom)
            InitializeReactiveRandomBlobData(_blobData, numberOfBlobs);
        else
            InitializeRandomBlobData(_blobData, 8); // Assuming Random state still uses 8 blobs

        CreateComputeBuffer();
    }

    void CreateComputeBuffer()
    {
        if (_blobBuffer != null)
            _blobBuffer.Release();

        _blobBuffer = new ComputeBuffer(_blobData.Length, Marshal.SizeOf(typeof(BlobData)));
        UpdateShaderProperties();
    }

    void UpdateShaderProperties()
    {
        if (metaballMaterial != null)
        {
            _blobBuffer.SetData(_blobData);
            metaballMaterial.SetBuffer("_BlobBuffer", _blobBuffer);
            metaballMaterial.SetInt("_BlobCount", _blobData.Length);
            metaballMaterial.SetFloat("_CameraZOffset", cameraZOffset);
            metaballMaterial.SetFloat("_CameraFOVFactor", cameraFOVFactor);
            metaballMaterial.SetFloat("_TimeMultiplier", timeMultiplier);
        }
    }

    void OnDestroy()
    {
        if (_blobBuffer != null)
        {
            _blobBuffer.Release();
            _blobBuffer = null;
        }
    }

    void Update()
    {
        if (metaballMaterial == null) return;

        AnalyzeAudio();
        UpdateBlobs();
        UpdateShaderProperties();
    }

    void AnalyzeAudio()
    {
        if (audioSource != null && audioSampleCount > 0)
        {
            if (_audioData == null || _audioData.Length != audioSampleCount)
                _audioData = new float[audioSampleCount];

            audioSource.GetSpectrumData(_audioData, 0, FFTWindow.BlackmanHarris);

            float rms = 0;
            for (int i = 0; i < audioSampleCount; i++)
                rms += _audioData[i] * _audioData[i];
            rms = Mathf.Sqrt(rms / audioSampleCount);

            _currentAudioLevel = Mathf.Lerp(_currentAudioLevel, rms * audioSensitivity, audioSmoothness);
        }
        else
        {
            _currentAudioLevel = Mathf.Lerp(_currentAudioLevel, 0f, audioSmoothness * Time.deltaTime * 10f);
        }
    }

    void UpdateBlobs()
    {
        float time = Time.time * timeMultiplier;

        switch (_currentState)
        {
            case AnimationState.Random:
                UpdateRandomState(time);
                break;

            case AnimationState.ReactiveRandom:
                UpdateReactiveRandomState(time);
                break;

            case AnimationState.ToReactiveRandom:
                UpdateTransitionToReactiveRandom();
                break;

            case AnimationState.ToRandom:
                UpdateTransitionToRandom();
                break;

            case AnimationState.ScreenSaver:
                UpdateScreenSaverState(time);
                break;

            case AnimationState.ToScreenSaver:
                UpdateTransitionToScreenSaver();
                break;
        }
    }

    // This is now the old ScreenSaver functionality
    void UpdateRandomState(float time)
    {
        float horizontalMovement = Mathf.Sin(time * screenSaverSpeed * 0.25f) * (screenSaverHorizontalRange - screenSaverPadding);
        float verticalMovement = Mathf.Cos(time * screenSaverSpeed * 0.15f) * screenSaverVerticalFloat;

        Vector3 groupCenter = screenSaverCenterPosition + new Vector3(
            horizontalMovement,
            verticalMovement,
            Mathf.Sin(time * screenSaverSpeed * 0.1f) * 0.3f
        );

        for (int i = 0; i < _blobData.Length; i++)
        {
            float scaledAmplitude = screenSaverGroupRadius * screenSaverScale;
            Vector3 localMovement = new Vector3(
                Mathf.Sin(time * animationSpeed * screenSaverSpeed + i * 1.5f) * scaledAmplitude,
                Mathf.Cos(time * animationSpeed * screenSaverSpeed + i * 1.0f) * scaledAmplitude * 0.75f,
                Mathf.PerlinNoise(time * 0.5f + i * 0.3f, time * 0.4f + i * 0.5f) * scaledAmplitude * 0.5f - scaledAmplitude * 0.25f
            );

            Vector3 targetPos = groupCenter + localMovement;
            _blobData[i].position = Vector3.Lerp(_blobData[i].position, targetPos, Time.deltaTime * transitionSmoothness * 1.5f);

            float baseRadius = Mathf.Lerp(blobRadiusMin, blobRadiusMax, (float)i / _blobData.Length);
            float targetRadius = baseRadius * screenSaverScale;
            _blobData[i].radius = Mathf.Lerp(_blobData[i].radius, targetRadius, Time.deltaTime * transitionSmoothness);

            Color.RGBToHSV(_blobData[i].color, out float h, out float s, out float v);
            h = Mathf.Repeat(h + Time.deltaTime * screenSaverColorShiftSpeed, 1f);
            s = Mathf.Lerp(s, blobColorMaxSaturation, Time.deltaTime * transitionSmoothness);
            v = Mathf.Lerp(v, blobColorMaxValue, Time.deltaTime * transitionSmoothness);
            _blobData[i].color = Color.HSVToRGB(h, s, v);
        }
    }

    // This is now the old Random functionality
    void UpdateScreenSaverState(float time)
    {
        float cameraTime = time * cameraAnimationMultiplier;
        float amplitude = baseRandomAmplitude;

        for (int i = 0; i < _blobData.Length; i++)
        {
            // Position
            _blobData[i].position = new Vector3(
                Mathf.Sin(time * animationSpeed + i * 1.5f) * amplitude,
                Mathf.Cos(time * animationSpeed + i * 1.0f) * amplitude * 0.75f,
                Mathf.PerlinNoise(time * 0.5f + i * 0.3f, time * 0.4f + i * 0.5f) * amplitude * 0.5f - amplitude * 0.25f
            );

            // Color animation
            Color.RGBToHSV(_blobData[i].color, out float h, out float s, out float v);
            h = Mathf.Repeat(h + Time.deltaTime * blobColorShiftSpeed, 1f);
            s = Mathf.Lerp(s, blobColorMaxSaturation, Time.deltaTime * 5f);
            v = Mathf.Lerp(v, blobColorMaxValue, Time.deltaTime * 5f);
            _blobData[i].color = Color.HSVToRGB(h, s, v);
        }
    }

    void UpdateReactiveRandomState(float time)
    {
        float cameraTime = time * cameraAnimationMultiplier;
        int spectrumBands = Mathf.Max(1, audioSampleCount / 2);

        for (int i = 0; i < _blobData.Length; i++)
        {
            float bandAmplitude = (i == 0 && _blobData.Length > 0)
                ? _currentAudioLevel
                : _audioData[(i - ((_blobData.Length > 0 && i == 0) ? 0 : 1) % spectrumBands)] * audioSensitivity;

            float audioEffect = bandAmplitude * _blobData[i].audioSensitivityMultiplier * reactiveRandomOverallAudioScale;

            float movementAmplitude = reactiveGroupRadius * (1.0f + audioEffect * reactiveRandomMovementAudioScale * 0.5f);

            float targetRadius = Mathf.Clamp(
                (blobRadiusMin + blobRadiusMax) * 0.5f * reactiveScale * (1.0f + audioEffect * reactiveRandomRadiusAudioScale * 0.3f),
                blobRadiusMin * reactiveScale,
                blobRadiusMax * reactiveScale
            );

            float smooth = 1.0f - Mathf.Exp(-reactiveRandomSmoothing * Time.deltaTime);

            Vector3 localMovement = new Vector3(
                Mathf.Sin(time * animationSpeed + i * 1.5f) * movementAmplitude,
                Mathf.Cos(time * animationSpeed + i * 1.0f) * movementAmplitude * 0.5f,
                Mathf.PerlinNoise(time * 0.5f + i * 0.3f, time * 0.4f + i * 0.5f) * movementAmplitude * 0.3f
            );
            
            Vector3 targetPos = reactiveCenterPosition + localMovement;
            _blobData[i].position = Vector3.Lerp(_blobData[i].position, targetPos, smooth);

            _blobData[i].radius = Mathf.Lerp(_blobData[i].radius, targetRadius, smooth);

            Color.RGBToHSV(_blobData[i].color, out float h, out float s, out float v);
            h = Mathf.Repeat(h + Time.deltaTime * blobColorShiftSpeed, 1f);
            s = Mathf.Lerp(s, blobColorMaxSaturation, Time.deltaTime * 5f);
            v = Mathf.Lerp(v, blobColorMaxValue, Time.deltaTime * 5f);
            _blobData[i].color = Color.HSVToRGB(h, s, v);
        }
    }

    void UpdateTransitionToReactiveRandom()
    {
        float t = Mathf.Clamp01((Time.time - _transitionStartTime) / _transitionDuration);
        float smoothT = Mathf.SmoothStep(0, 1, t);
        int maxBlobs = Mathf.Max(_startBlobData.Length, _targetBlobData.Length);

        for (int i = 0; i < maxBlobs; i++)
        {
            if (i >= _blobData.Length) continue;

            bool hasStart = i < _startBlobData.Length;
            bool hasTarget = i < _targetBlobData.Length;

            BlobData start = hasStart ? _startBlobData[i] : GetEmptyBlob();
            BlobData target = hasTarget ? _targetBlobData[i] : GetEmptyBlob();

            _blobData[i].position = Vector3.Lerp(start.position, target.position, smoothT);
            _blobData[i].radius = Mathf.Lerp(start.radius, target.radius, smoothT);
            _blobData[i].color = Color.Lerp(start.color, target.color, smoothT);
            _blobData[i].audioSensitivityMultiplier = Mathf.Lerp(start.audioSensitivityMultiplier, target.audioSensitivityMultiplier, smoothT);
        }

        if (t >= 1f)
        {
            _currentState = AnimationState.ReactiveRandom;
        }
    }

    // This is now the transition to the old ScreenSaver state
    void UpdateTransitionToRandom()
    {
        float t = Mathf.Clamp01((Time.time - _transitionStartTime) / _transitionDuration);
        float smoothT = Mathf.SmoothStep(0, 1, t);
        int maxBlobs = Mathf.Max(_startBlobData.Length, screenSaverBlobCount);

        // Adjust blob array size if needed
        if (_blobData.Length != screenSaverBlobCount && t < 1f)
        {
            BlobData[] newBlobData = new BlobData[screenSaverBlobCount];
            for (int i = 0; i < Mathf.Min(_blobData.Length, screenSaverBlobCount); i++)
            {
                newBlobData[i] = _blobData[i];
            }
            _blobData = newBlobData;
            CreateComputeBuffer();
        }

        for (int i = 0; i < maxBlobs; i++)
        {
            if (i >= _blobData.Length) continue;

            bool hasStart = i < _startBlobData.Length;
            bool hasTarget = i < _targetBlobData.Length;

            BlobData start = hasStart ? _startBlobData[i] : GetEmptyBlob();
            BlobData target = hasTarget ? _targetBlobData[i] : GetEmptyBlob();

            _blobData[i].position = Vector3.Lerp(start.position, target.position, smoothT);
            _blobData[i].radius = Mathf.Lerp(start.radius, target.radius, smoothT);
            _blobData[i].color = Color.Lerp(start.color, target.color, smoothT);
            _blobData[i].audioSensitivityMultiplier = Mathf.Lerp(start.audioSensitivityMultiplier, 0f, smoothT);
        }

        // Fade out extra blobs
        for (int i = screenSaverBlobCount; i < _blobData.Length; i++)
        {
            _blobData[i].radius = Mathf.Lerp(_startBlobData[i].radius, 0f, smoothT);
            _blobData[i].color = Color.Lerp(_startBlobData[i].color, Color.clear, smoothT);
        }

        if (t >= 1f)
        {
            _currentState = AnimationState.Random;
        }
    }

    // This is now the transition to the old Random state
    void UpdateTransitionToScreenSaver()
    {
        float t = Mathf.Clamp01((Time.time - _transitionStartTime) / _transitionDuration);
        float smoothT = Mathf.SmoothStep(0, 1, t);
        int maxBlobs = Mathf.Max(_startBlobData.Length, 8);

        // Adjust blob array size if needed
        if (_blobData.Length != 8 && t < 1f)
        {
            BlobData[] newBlobData = new BlobData[8];
            for (int i = 0; i < Mathf.Min(_blobData.Length, 8); i++)
            {
                newBlobData[i] = _blobData[i];
            }
            _blobData = newBlobData;
            CreateComputeBuffer();
        }

        for (int i = 0; i < maxBlobs; i++)
        {
            if (i >= _blobData.Length) continue;

            bool hasStart = i < _startBlobData.Length;
            bool hasTarget = i < _targetBlobData.Length;

            BlobData start = hasStart ? _startBlobData[i] : GetEmptyBlob();
            BlobData target = hasTarget ? _targetBlobData[i] : GetEmptyBlob();

            _blobData[i].position = Vector3.Lerp(start.position, target.position, smoothT);
            _blobData[i].radius = Mathf.Lerp(start.radius, target.radius, smoothT);
            _blobData[i].color = Color.Lerp(start.color, target.color, smoothT);
            _blobData[i].audioSensitivityMultiplier = Mathf.Lerp(start.audioSensitivityMultiplier, 1f, smoothT);
        }

        // Fade out extra blobs if transitioning from a state with more than 8 blobs
        for (int i = 8; i < _blobData.Length; i++)
        {
            _blobData[i].radius = Mathf.Lerp(_startBlobData[i].radius, 0f, smoothT);
            _blobData[i].color = Color.Lerp(_startBlobData[i].color, Color.clear, smoothT);
        }

        if (t >= 1f)
        {
            _currentState = AnimationState.ScreenSaver;
        }
    }

    // This is now the old ScreenSaver initialization
    void InitializeRandomBlobData(BlobData[] data, int count)
    {
        for (int i = 0; i < count; i++)
        {
            float scaledAmplitude = screenSaverGroupRadius * screenSaverScale;
            Vector3 localOffset = new Vector3(
                Mathf.Sin(i * 1.5f) * scaledAmplitude,
                Mathf.Cos(i * 1.0f) * scaledAmplitude * 0.75f,
                (i % 2 == 0 ? 1 : -1) * scaledAmplitude * 0.3f
            );
            Vector3 startPos = screenSaverCenterPosition + localOffset;
            float baseRadius = Mathf.Lerp(blobRadiusMin, blobRadiusMax, (float)i / count);

            data[i] = new BlobData
            {
                position = startPos,
                radius = baseRadius * screenSaverScale,
                color = Random.ColorHSV(0, 1, blobColorMaxSaturation * 0.8f, blobColorMaxSaturation, blobColorMaxValue * 0.8f, blobColorMaxValue),
                audioSensitivityMultiplier = 0f
            };
        }
    }

    // This is now the old Random initialization
    void InitializeScreenSaverBlobData(BlobData[] data, int count)
    {
        for (int i = 0; i < count; i++)
        {
            data[i] = new BlobData
            {
                position = Random.insideUnitSphere * baseRandomAmplitude,
                radius = Random.Range(blobRadiusMin, blobRadiusMax),
                color = Random.ColorHSV(0, 1, blobColorMaxSaturation * 0.8f, blobColorMaxSaturation, blobColorMaxValue * 0.8f, blobColorMaxValue),
                audioSensitivityMultiplier = 1f
            };
        }
    }

    void InitializeReactiveRandomBlobData(BlobData[] data, int count)
    {
        for (int i = 0; i < count; i++)
        {
            Vector3 localOffset = new Vector3(
                Mathf.Sin(i * 1.5f) * reactiveGroupRadius,
                Mathf.Cos(i * 1.0f) * reactiveGroupRadius * 0.5f,
                (i % 2 == 0 ? 1 : -1) * reactiveGroupRadius * 0.3f
            );
            
            data[i] = new BlobData
            {
                position = reactiveCenterPosition + localOffset,
                radius = Random.Range(blobRadiusMin, blobRadiusMax) * reactiveScale,
                color = Random.ColorHSV(0, 1, blobColorMaxSaturation * 0.8f, blobColorMaxSaturation, blobColorMaxValue * 0.8f, blobColorMaxValue),
                audioSensitivityMultiplier = Random.Range(reactiveRandomAudioSensitivityMin, reactiveRandomAudioSensitivityMax)
            };
        }
    }

    BlobData GetEmptyBlob()
    {
        return new BlobData
        {
            position = Vector3.zero,
            radius = 0.0001f,
            color = Color.black,
            audioSensitivityMultiplier = 0f
        };
    }

    public void MoveBlobsToReactiveRandom(float duration)
    {
        if (_currentState == AnimationState.ReactiveRandom || _currentState == AnimationState.ToReactiveRandom)
            return;

        _startBlobData = (BlobData[])_blobData.Clone();
        _targetBlobData = new BlobData[numberOfBlobs];
        
        // Calculate target positions for reactive state
        for (int i = 0; i < numberOfBlobs; i++)
        {
            Vector3 localOffset = new Vector3(
                Mathf.Sin(i * 1.5f) * reactiveGroupRadius,
                Mathf.Cos(i * 1.0f) * reactiveGroupRadius * 0.5f,
                (i % 2 == 0 ? 1 : -1) * reactiveGroupRadius * 0.3f
            );
            
            _targetBlobData[i] = new BlobData
            {
                position = reactiveCenterPosition + localOffset,
                radius = Random.Range(blobRadiusMin, blobRadiusMax) * reactiveScale,
                color = Random.ColorHSV(0, 1, blobColorMaxSaturation * 0.8f, blobColorMaxSaturation, blobColorMaxValue * 0.8f, blobColorMaxValue),
                audioSensitivityMultiplier = Random.Range(reactiveRandomAudioSensitivityMin, reactiveRandomAudioSensitivityMax)
            };
        }
        
        // Resize blob array if needed
        if (_blobData.Length != numberOfBlobs)
        {
            BlobData[] newBlobData = new BlobData[numberOfBlobs];
            for (int i = 0; i < numberOfBlobs; i++)
            {
                if (i < _blobData.Length)
                {
                    newBlobData[i] = _blobData[i];
                }
                else
                {
                    newBlobData[i] = GetEmptyBlob();
                }
            }
            _blobData = newBlobData;
            CreateComputeBuffer();
        }

        _currentState = AnimationState.ToReactiveRandom;
        _transitionStartTime = Time.time;
        _transitionDuration = duration;
    }

    // This is now the old "EnterScreenSaverMode" functionality
    public void ResumeRandomAnimation(float duration)
    {
        if (_currentState == AnimationState.Random || _currentState == AnimationState.ToRandom)
            return;

        _startBlobData = (BlobData[])_blobData.Clone();
        _targetBlobData = new BlobData[screenSaverBlobCount];

        // Calculate smooth target positions for ScreenSaver state based on current time
        float time = Time.time * timeMultiplier;
        float horizontalMovement = Mathf.Sin(time * screenSaverSpeed * 0.25f) * (screenSaverHorizontalRange - screenSaverPadding);
        float verticalMovement = Mathf.Cos(time * screenSaverSpeed * 0.15f) * screenSaverVerticalFloat;
        Vector3 groupCenter = screenSaverCenterPosition + new Vector3(
            horizontalMovement,
            verticalMovement,
            Mathf.Sin(time * screenSaverSpeed * 0.1f) * 0.3f
        );

        for (int i = 0; i < screenSaverBlobCount; i++)
        {
            float scaledAmplitude = screenSaverGroupRadius * screenSaverScale;
            Vector3 localMovement = new Vector3(
                Mathf.Sin(time * animationSpeed * screenSaverSpeed + i * 1.5f) * scaledAmplitude,
                Mathf.Cos(time * animationSpeed * screenSaverSpeed + i * 1.0f) * scaledAmplitude * 0.75f,
                Mathf.PerlinNoise(time * 0.5f + i * 0.3f, time * 0.4f + i * 0.5f) * scaledAmplitude * 0.5f - scaledAmplitude * 0.25f
            );
            Vector3 targetPos = groupCenter + localMovement;
            float baseRadius = Mathf.Lerp(blobRadiusMin, blobRadiusMax, (float)i / screenSaverBlobCount);

            _targetBlobData[i] = new BlobData
            {
                position = targetPos,
                radius = baseRadius * screenSaverScale,
                color = Random.ColorHSV(0, 1, blobColorMaxSaturation * 0.8f, blobColorMaxSaturation, blobColorMaxValue * 0.8f, blobColorMaxValue),
                audioSensitivityMultiplier = 0f
            };
        }

        if (_blobData.Length != screenSaverBlobCount)
        {
            BlobData[] newBlobData = new BlobData[screenSaverBlobCount];
            for (int i = 0; i < screenSaverBlobCount; i++)
            {
                if (i < _blobData.Length)
                {
                    newBlobData[i] = _blobData[i];
                }
                else
                {
                    newBlobData[i] = GetEmptyBlob();
                }
            }
            _blobData = newBlobData;
            CreateComputeBuffer();
        }

        _currentState = AnimationState.ToRandom;
        _transitionStartTime = Time.time;
        _transitionDuration = duration;
    }

    // This is now the old "ResumeRandomAnimation" functionality
    public void EnterScreenSaverMode(float duration)
    {
        if (_currentState == AnimationState.ScreenSaver || _currentState == AnimationState.ToScreenSaver)
            return;

        _startBlobData = (BlobData[])_blobData.Clone();
        _targetBlobData = new BlobData[8];

        float time = Time.time * timeMultiplier;
        for (int i = 0; i < 8; i++)
        {
            Vector3 targetPos = new Vector3(
                Mathf.Sin(time * animationSpeed + i * 1.5f) * baseRandomAmplitude,
                Mathf.Cos(time * animationSpeed + i * 1.0f) * baseRandomAmplitude * 0.75f,
                Mathf.PerlinNoise(time * 0.5f + i * 0.3f, time * 0.4f + i * 0.5f) * baseRandomAmplitude * 0.5f - baseRandomAmplitude * 0.25f
            );

            _targetBlobData[i] = new BlobData
            {
                position = targetPos,
                radius = Random.Range(blobRadiusMin, blobRadiusMax),
                color = Random.ColorHSV(0, 1, blobColorMaxSaturation * 0.8f, blobColorMaxSaturation, blobColorMaxValue * 0.8f, blobColorMaxValue),
                audioSensitivityMultiplier = 1f
            };
        }

        if (_blobData.Length != 8)
        {
            BlobData[] newBlobData = new BlobData[8];
            for (int i = 0; i < 8; i++)
            {
                if (i < _blobData.Length)
                {
                    newBlobData[i] = _blobData[i];
                }
                else
                {
                    newBlobData[i] = GetEmptyBlob();
                }
            }
            _blobData = newBlobData;
            CreateComputeBuffer();
        }

        _currentState = AnimationState.ToScreenSaver;
        _transitionStartTime = Time.time;
        _transitionDuration = duration;
    }

    public void ExitScreenSaverMode(float duration)
    {
        if (_currentState == AnimationState.ScreenSaver || _currentState == AnimationState.ToScreenSaver)
        {
            ResumeRandomAnimation(duration);
        }
    }

    public bool IsInScreenSaverMode()
    {
        return _currentState == AnimationState.ScreenSaver || _currentState == AnimationState.ToScreenSaver;
    }
}