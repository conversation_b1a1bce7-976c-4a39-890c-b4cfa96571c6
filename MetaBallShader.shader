Shader "Unlit/TransparentMetaballsBackgroundRemoved_URP6_Controlled_NoFog"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        iChannel0 ("Texture iChannel0", Cube) = "white" {}
        // Removed: _FogColor ("Fog Color", Color) = (0.5, 0.5, 0.5, 1)
        // Removed: _FogDensity ("Fog Density", Float) = 0.1
        _TimeMultiplier ("Time Multiplier", Float) = 1.0
        _AnimationTime ("Animation Time (Script Controlled)", Float) = 0.0

        _CameraZOffset ("Camera Z Offset", Float) = 0.0
        _CameraFOVFactor ("Camera FOV Factor", Float) = 2.0

        _AnimationState ("Animation State", Float) = 0.0
    }
    SubShader
    {
        Tags { "RenderType"="Transparent" "Queue"="Transparent" }
        LOD 100

        Pass
        {
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            // Removed: #pragma multi_compile_fog

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareNormalsTexture.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Input.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float2 uv : TEXCOORD0;
                // Removed: float fogFactor : TEXCOORD1;
                float4 positionHClip : SV_POSITION;
            };

            Texture2D _MainTex;
            SamplerState sampler_MainTex;
            TextureCube iChannel0;
            SamplerState sampler_iChannel0;
            // Removed: float4 _FogColor;
            // Removed: float _FogDensity;
            float _TimeMultiplier;
            float _AnimationTime;

            float _CameraZOffset;
            float _CameraFOVFactor;

            float _AnimationState;

            #define samples 1

            // Struct to hold blob data including color and audio sensitivity multiplier
            struct BlobData
            {
                float3 position;
                float radius;
                float4 color;
                float audioSensitivityMultiplier; // New: Per-blob audio sensitivity multiplier
            };

            // StructuredBuffer to receive blob data from C#
            StructuredBuffer<BlobData> _BlobBuffer;
            // Uniform to receive the actual number of blobs
            uint _BlobCount;


            Varyings vert (Attributes v)
            {
                Varyings o;
                o.positionHClip = TransformObjectToHClip(v.positionOS.xyz);
                o.uv = v.uv;
                // Removed fog factor calculation:
                // float depth = -o.positionHClip.z / o.positionHClip.w;
                // o.fogFactor = exp2(-_FogDensity * _FogDensity * depth * depth);
                // o.fogFactor = saturate(o.fogFactor);
                return o;
            }

            float sdMetaBalls(float3 pos)
            {
                float m = 0.0;
                float p = 0.0;
                float dmin = 1e20;
                float h = 1.0;

                for (uint i = 0; i < _BlobCount; i++)
                {
                    float3 blobPos = _BlobBuffer[i].position;
                    float blobRadius = _BlobBuffer[i].radius;

                    float db = length(blobPos - pos);
                    if (db < blobRadius)
                    {
                        float x = db / blobRadius;
                        p += 1.0 - x * x * x * (x * (x * 6.0 - 15.0) + 10.0);
                        m += 1.0;
                        h = max(h, 0.5333 * blobRadius);
                    }
                    else
                    {
                        dmin = min(dmin, db - blobRadius);
                    }
                }
                float d = dmin + 0.1;
                if (m > 0.5)
                {
                    float th = 0.2;
                    d = h * (th - p);
                }
                return d;
            }

            float3 norMetaBalls(float3 pos)
            {
                #define ANALYTIC_NORMALS
                #ifdef ANALYTIC_NORMALS
                    float3 nor = float3(0.0, 0.0001, 0.0);
                    for (uint i = 0; i < _BlobCount; i++)
                    {
                        float3 blobPos = _BlobBuffer[i].position;
                        float blobRadius = _BlobBuffer[i].radius;

                        if (blobRadius > 0.0001)
                        {
                            float db = length(blobPos - pos);
                            float x = clamp(db / blobRadius, 0.0, 1.0);
                            float p = x * x * (30.0 * x * x - 60.0 * x + 30.0);
                            nor += normalize(pos - blobPos) * p / blobRadius;
                        }
                    }
                    return normalize(nor);
                #else
                    float3 eps = float3(precis, 0.0, 0.0);
                    return normalize(float3(
                        map(pos + eps.xyy) - map(pos - eps.xyy),
                        map(pos + eps.yxy) - map(pos - eps.yxy),
                        map(pos + eps.yyx) - map(pos - eps.yyx)));
                #endif
            }

            float map(in float3 p)
            {
                return sdMetaBalls(p);
            }

            static const float precis = 0.01;

            float2 intersect(in float3 ro, in float3 rd)
            {
                float maxd = 15.0;
                float h = precis * 2.0;
                float t = 0.0;
                float m = 1.0;
                for (int i = 0; i < 75; i++)
                {
                    if (h < precis || t > maxd) break;
                    t += h;
                    h = map(ro + rd * t);
                }
                if (t > maxd) m = -1.0;
                return float2(t, m);
            }

            float3 calcNormal(in float3 pos)
            {
                #define ANALYTIC_NORMALS
                #ifdef ANALYTIC_NORMALS
                    return norMetaBalls(pos);
                #else
                    float3 eps = float3(precis, 0.0, 0.0);
                    return normalize(float3(
                        map(pos + eps.xyy) - map(pos - eps.xyy),
                        map(pos + eps.yxy) - map(pos - eps.yxy),
                        map(pos + eps.yyx) - map(pos - eps.yyx)));
                #endif
            }

            float4 frag (Varyings i) : SV_Target
            {
                float2 q = i.uv;
                float2 m = float2(0.5, 0.5);

                float msamples = sqrt(float(samples));
                float3 tot = float3(0.0, 0.0, 0.0);
                float hit = 0.0;
                float3 col = float3(0.0, 0.0, 0.0);

                #if samples > 1
                for (int a = 0; a < samples; a++)
                #else
                float a = 0.0;
                #endif
                {
                    float2 poff = float2(fmod(float(a), msamples), floor(float(a) / msamples)) / msamples;
                    float toff = 0.0;

                    float time = _AnimationTime + toff;

                    float an = 0.5 * time - 6.2831 * (m.x - 0.5);
                    float3 ro = float3(5.0 * sin(an), 2.5 * cos(0.4 * an), -8.0 * cos(an) + _CameraZOffset);
                    float3 ta = float3(0.0, 0.0, 0.0);

                    float2 p = -1.0 + 2.0 * (q * _ScreenParams.xy + poff) / _ScreenParams.xy;
                    p.x *= _ScreenParams.x / _ScreenParams.y;
                    p.x *= -1;

                    float3 ww = normalize(ta - ro);
                    float3 uu = normalize(cross(ww, float3(0.0, 1.0, 0.0)));
                    float3 vv = normalize(cross(uu, ww));

                    float3 rd = normalize(p.x * uu + p.y * vv + _CameraFOVFactor * ww);

                    float3 bkgColor = pow(iChannel0.Sample(sampler_iChannel0, rd).xyz, float3(2.2, 2.2, 2.2));

                    float2 tmat = intersect(ro, rd);
                    if (tmat.y > -0.5)
                    {
                        float3 pos = ro + tmat.x * rd;
                        float3 nor = calcNormal(pos);
                        float3 ref = reflect(rd, nor);

                        float3 mate = float3(0.0, 0.0, 0.0);
                        float w = 0.01;

                        for (uint j = 0; j < _BlobCount; j++)
                        {
                            float3 blobPos = _BlobBuffer[j].position;
                            float blobRadius = _BlobBuffer[j].radius;
                            float3 ccc = _BlobBuffer[j].color.rgb; // Get color directly from buffer

                            float x = clamp(length(blobPos - pos) / blobRadius, 0.0, 1.0);
                            float p = 1.0 - x * x * (3.0 - 2.0 * x);

                            mate += p * ccc;
                            w += p;
                        }
                        mate /= max(w, 0.001);

                        float3 lin = float3(0.0, 0.0, 0.0);
                        lin += lerp(float3(0.05, 0.02, 0.0), 1.2 * float3(0.8, 0.9, 1.0), 0.5 + 0.5 * nor.y);
                        lin *= 1.0 + 1.5 * float3(0.7, 0.5, 0.3) * pow(clamp(1.0 + dot(nor, rd), 0.0, 1.0), 2.0);
                        lin += 1.5 * clamp(0.3 + 2.0 * nor.y, 0.0, 1.0) * pow(iChannel0.Sample(sampler_iChannel0, ref).xyz, float3(2.2, 2.2, 2.2)) * (0.04 + 0.96 * pow(clamp(1.0 + dot(nor, rd), 0.0, 1.0), 4.0));

                        col = lin * mate;
                        hit = 1.0;
                    }
                    else
                    {
                        col = bkgColor;
                    }
                    tot += col;
                }
                tot /= float(samples);

                tot = pow(clamp(tot, 0.0, 1.0), float3(0.45, 0.45, 0.45));
                tot *= 0.5 + 0.5 * pow(16.0 * q.x * q.y * (1.0 - q.x) * (1.0 - q.y), 0.15);

                // Removed fog application:
                // float3 finalColor = lerp(tot, _FogColor.rgb, 1.0 - i.fogFactor);
                float3 finalColor = tot; // Directly use the calculated color

                return float4(finalColor, hit);
            }
            ENDHLSL
        }
    }
}