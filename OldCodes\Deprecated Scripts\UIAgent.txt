using UnityEngine;
using System.Collections.Generic;

// Script that calculates and logs the 2D distance to detected VuMarks
public class UIAgent : MonoBehaviour
{
    void Update()
    {
        // Check if the VuMarkPoseManager instance exists
        if (VuMarkExistenceManager.Instance != null)
        {
            // Get the position of the GameObject this script is attached to
            Vector3 currentObjectPosition = this.transform.position;

            // Get a list of all currently detected VuMarks
            List<VuMarkInfoInScene> detectedMarks = VuMarkExistenceManager.Instance.GetAllVuMarksInScene();

            if (detectedMarks.Count > 0)
            {
                Debug.Log($"Detected {detectedMarks.Count} VuMarks. Calculating 2D distances:");
                foreach (var vuMarkInfo in detectedMarks)
                {
                    // Get the VuMark's position
                    Vector3 vuMarkPosition = vuMarkInfo.Position;

                    // Calculate the 2D distance, ignoring the Y-axis (horizontal distance on XZ plane)
                    Vector3 position2D_Current = new Vector3(currentObjectPosition.x, 0, currentObjectPosition.z);
                    Vector3 position2D_VuMark = new Vector3(vuMarkPosition.x, 0, vuMarkPosition.z);
                    float distance2D_XZ = Vector3.Distance(position2D_Current, position2D_VuMark);

                    // Log the VuMark ID and the calculated 2D distance
                    Debug.Log($"VuMark ID: {vuMarkInfo.Id}, 2D (XZ) Distance: {distance2D_XZ:F2} units"); // :F2 formats to 2 decimal places

                    // You can now use this pose information and 2D distance for other logic
                    // For example, trigger an action if a VuMark is within a certain 2D distance:
                    // if (distance2D_XZ < 0.3f) // Example threshold for 2D XZ distance
                    // {
                    //     Debug.Log($"VuMark ID {vuMarkInfo.Id} is horizontally close!");
                    //     // Perform an action
                    // }
                }
            }
            // else
            // {
            //     Debug.Log("No VuMarks currently detected."); // Uncomment if you want this log every frame
            // }

            // The specific VuMark check examples are removed for simplicity as requested.
        }
        // else
        // {
        //     Debug.LogWarning("VuMarkPoseManager instance not found!"); // Should not happen if setup correctly
        // }
    }
}
