Shader "UI/URP_FrostedGlass"
{
    Properties
    {
        _MainTex ("UI Sprite (Optional)", 2D) = "white" {} // Your actual UI sprite (e.g., for icons)
        _Color ("UI Tint Color", Color) = (1,1,1,1) // Overall tint for UI sprite and background
        _AlphaMultiplier ("Overall Alpha Multiplier", Range(0,1)) = 1

        [Header(Frosted Glass Effect)]
        _BlurRadius ("Blur Radius (Pixels)", Range(0, 10)) = 2 // How far to sample for blur
        _BlurSamples ("Blur Samples", Range(4, 16)) = 9 // Number of samples for box blur (must be > 0)
        _DistortionNoise ("Distortion Noise (Greyscale)", 2D) = "white" {} // Texture for distortion (e.g., perlin noise)
        _DistortionStrength ("Distortion Strength", Range(0, 0.1)) = 0.01 // How strong the distortion is
        _BackgroundOpacity ("Background Opacity", Range(0,1)) = 0.8 // How opaque the blurred background is

        // This is typically provided by URP, but declaring it ensures the property exists for inspector if needed
        // [HideInInspector] _CameraOpaqueTexture ("Opaque Texture", 2D) = "white" {} 
    }

    SubShader
    {
        Tags
        {
            "Queue" = "Transparent"
            "RenderType" = "Transparent"
            "RenderPipeline" = "UniversalPipeline"
            "IgnoreProjector" = "True"
            "PreviewType" = "Plane"
        }

        Blend SrcAlpha OneMinusSrcAlpha // Standard alpha blending
        Cull Off // UI elements typically don't cull
        ZWrite Off // Don't write to Z-buffer for UI

        Pass
        {
            Name "FrostedGlass"

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl" // For some utilities if needed, but not strictly for this

            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
                float4 color : COLOR; // Vertex color from UI system
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4 color : COLOR;
                float4 screenUV : TEXCOORD1; // Screen-space UVs for _CameraOpaqueTexture
            };

            TEXTURE2D(_MainTex);
            SamplerState sampler_MainTex;

            // URP's Opaque Texture. Available when "Opaque Texture" is enabled in URP Asset.
            TEXTURE2D(_CameraOpaqueTexture);
            SamplerState sampler_CameraOpaqueTexture;

            TEXTURE2D(_DistortionNoise);
            SamplerState sampler_DistortionNoise;

            float4 _Color;
            float _AlphaMultiplier;
            float _BlurRadius;
            float _BlurSamples; // Use a float here, cast to int for loop
            float _DistortionStrength;
            float _BackgroundOpacity;

            Varyings vert(Attributes input)
            {
                Varyings output;
                output.positionCS = TransformObjectToHClip(input.positionOS.xyz);
                output.uv = input.uv;
                output.color = input.color;

                // Calculate screen-space UVs for sampling _CameraOpaqueTexture
                output.screenUV = ComputeScreenPos(output.positionCS);
                output.screenUV.xy /= output.screenUV.w; // Perspective divide (normalize to 0-1 range)

                // Adjust for platform differences (e.g., OpenGL might have UVs start at bottom)
                #if UNITY_UV_STARTS_AT_TOP
                output.screenUV.y = 1.0 - output.screenUV.y;
                #endif

                return output;
            }

            float4 frag(Varyings input) : SV_Target
            {
                // Calculate pixel size for UV offsets
                // _ScreenParams.zw contains 1.0/width and 1.0/height
                float2 pixelSize = _ScreenParams.zw; // 1/width, 1/height

                // Apply distortion
                float2 distortedScreenUV = input.screenUV.xy;
                if (_DistortionStrength > 0.0001f) // Only sample if distortion is enabled
                {
                    float2 noise = SAMPLE_TEXTURE2D(_DistortionNoise, sampler_DistortionNoise, input.screenUV.xy * 0.5 + _Time.y * 0.01).rg * 2.0 - 1.0; // Sample noise, make it -1 to 1
                    distortedScreenUV += noise * _DistortionStrength;
                }

                // Initialize blurred color
                float4 blurredBgColor = 0;
                
                // Box Blur - simple but effective for "frosted" look
                // Samples around the distortedScreenUV
                int numSamples = int(_BlurSamples); // Cast float to int for loop
                if (numSamples < 1) numSamples = 1; // Ensure at least one sample

                float sampleCount = 0;
                for (int y = -1; y <= 1; y++)
                {
                    for (int x = -1; x <= 1; x++)
                    {
                        if (sampleCount >= numSamples) break; // Limit samples if _BlurSamples is low
                        
                        float2 offsetUV = distortedScreenUV + float2(x, y) * pixelSize * _BlurRadius;
                        blurredBgColor += SAMPLE_TEXTURE2D(_CameraOpaqueTexture, sampler_CameraOpaqueTexture, offsetUV);
                        sampleCount++;
                    }
                    if (sampleCount >= numSamples) break;
                }
                
                if (sampleCount > 0)
                {
                    blurredBgColor /= sampleCount;
                }
                
                // Apply background opacity
                blurredBgColor.a = _BackgroundOpacity;

                // Sample the UI sprite (e.g., icon)
                float4 uiSpriteColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.uv);

                // Multiply by vertex color from UI system
                uiSpriteColor *= input.color;

                // Blend the UI sprite with the blurred background
                // The UI sprite's alpha determines how much it shows over the blurred background
                float4 finalColor = lerp(blurredBgColor, uiSpriteColor, uiSpriteColor.a);
                
                // Apply overall tint and alpha multiplier
                finalColor *= _Color;
                finalColor.a *= _AlphaMultiplier;

                return finalColor;
            }
            ENDHLSL
        }
    }
}