using UnityEngine;
using Vuforia;

public class DragOnPlane : MonoBehaviour
{
    private Camera arCamera;
    private Plane dragPlane;
    private bool isDragging = false;

    // Create instances of filters
    private OneEuroFilter positionXFilter;
    private OneEuroFilter positionYFilter;
    private OneEuroFilter positionZFilter;
    private OneEuroFilter<Quaternion> rotationFilter;

    private Vector3 smoothedPosition;
    private Quaternion smoothedRotation;
    private Vector3 previousPosition;
    private Quaternion previousRotation;

    // Filter parameters
    public float filterFrequency = 120.0f;
    public float positionSmoothingFactor = 0.1f; // Adjust this for smoother or more responsive movements
    public float rotationSmoothingFactor = 0.1f;

    private ObserverBehaviour observerBehaviour;

    void Start()
    {
        arCamera = Camera.main;
        smoothedPosition = transform.position;
        smoothedRotation = transform.rotation;
        previousPosition = transform.position;
        previousRotation = transform.rotation;

        // Initialize filters for each component of the position
        positionXFilter = new OneEuroFilter(filterFrequency);
        positionYFilter = new OneEuroFilter(filterFrequency);
        positionZFilter = new OneEuroFilter(filterFrequency);

        // Initialize filter for rotation
        rotationFilter = new OneEuroFilter<Quaternion>(filterFrequency);

        observerBehaviour = GetComponent<ObserverBehaviour>();
    }

    void Update()
    {
        // Apply EURO1 filter even when not dragging, based on Vuforia's updates
        if (observerBehaviour != null && observerBehaviour.TargetStatus.Status == Status.TRACKED)
        {
            // Get Vuforia's observed position and rotation
            Vector3 observedPosition = observerBehaviour.transform.position;
            Quaternion observedRotation = observerBehaviour.transform.rotation;

            // Apply EURO1 filter to position and rotation
            ApplyEURO1Filter(observedPosition, observedRotation);
        }

#if UNITY_EDITOR || UNITY_STANDALONE
        HandleMouseInput();
#else
        HandleTouchInput();
#endif
    }

    void HandleMouseInput()
    {
        Ray ray = arCamera.ScreenPointToRay(Input.mousePosition);

        if (Input.GetMouseButtonDown(0))
        {
            RaycastHit hit;
            if (Physics.Raycast(ray, out hit) && hit.transform == transform)
            {
                isDragging = true;
                dragPlane = new Plane(Vector3.up, transform.position);
            }
        }
        else if (Input.GetMouseButton(0) && isDragging)
        {
            dragPlane = new Plane(Vector3.up, transform.position); // Update every frame
            float distance;
            if (dragPlane.Raycast(ray, out distance))
            {
                Vector3 hitPoint = ray.GetPoint(distance);
                ApplyEURO1Filter(hitPoint, transform.rotation);
            }
        }
        else if (Input.GetMouseButtonUp(0))
        {
            isDragging = false;
        }
    }

    void HandleTouchInput()
    {
        if (Input.touchCount == 1)
        {
            Touch touch = Input.GetTouch(0);
            Ray ray = arCamera.ScreenPointToRay(touch.position);

            if (touch.phase == TouchPhase.Began)
            {
                RaycastHit hit;
                if (Physics.Raycast(ray, out hit) && hit.transform == transform)
                {
                    isDragging = true;
                    dragPlane = new Plane(Vector3.up, transform.position);
                }
            }
            else if (touch.phase == TouchPhase.Moved && isDragging)
            {
                dragPlane = new Plane(Vector3.up, transform.position); // Update every frame
                float distance;
                if (dragPlane.Raycast(ray, out distance))
                {
                    Vector3 hitPoint = ray.GetPoint(distance);
                    ApplyEURO1Filter(hitPoint, transform.rotation);
                }
            }
            else if (touch.phase == TouchPhase.Ended)
            {
                isDragging = false;
            }
        }
    }

    // This method applies the EURO1 filter to both position and rotation
    private void ApplyEURO1Filter(Vector3 newPosition, Quaternion newRotation)
    {
        // Apply EURO1 filter to each component of the position
        float filteredX = positionXFilter.Filter(newPosition.x);
        float filteredY = positionYFilter.Filter(newPosition.y);
        float filteredZ = positionZFilter.Filter(newPosition.z);

        // Reconstruct the filtered position
        smoothedPosition = new Vector3(filteredX, filteredY, filteredZ);
        transform.position = smoothedPosition;

        // Apply EURO1 filter to rotation
        smoothedRotation = rotationFilter.Filter(newRotation);
        transform.rotation = smoothedRotation;
    }
}
